- name: Ensure .ssh dir exists
  file:
    path: /runner/.ssh/
    state: directory

- name: Copy controller.pem
  copy:
    src: controller.pem
    dest: /runner/.ssh/controller.pem
  delegate_to: localhost
  run_once: true

- name: Set permission for controller.pem
  file:
    path: /runner/.ssh/controller.pem
    mode: '0400'

- name: Copy labms.pem
  copy:
    src: labms.pem
    dest: /runner/.ssh/labms.pem

- name: Set permission for labms.pem
  file:
    path: /runner/.ssh/labms.pem
    mode: '0400'

- name: Add SSH config with multiple keys
  blockinfile:
    path: /runner/.ssh/config
    create: yes
    mode: '0600'
    block: |
      Host *
        IdentityFile /runner/.ssh/controller.pem
        IdentityFile /runner/.ssh/labms.pem