- name: Setup multiple key access for EE
  hosts: localhost
  become: false
  roles:
    - setup-ssh-config

- name: Run command with dynamic ansible_user
  hosts: all
  gather_facts: false
  tasks:

    - name: Ambil user dari ansible_facts yang tersimpan
      set_fact:
        ansible_user: "{{ hostvars[inventory_hostname].ansible_user_id | default('ubuntu') }}"

    - name: Debug user yang dipakai
      debug:
        msg: "Connecting to {{ inventory_hostname }} as {{ ansible_user }}"

    - name: <PERSON><PERSON> koneksi via shell
      shell: ip route | grep default
      register: shell_result
      become: false

    - name: <PERSON><PERSON><PERSON>an output
      debug:
        msg: "Output dari {{ inventory_hostname }}: {{ shell_result.stdout_lines }}"
