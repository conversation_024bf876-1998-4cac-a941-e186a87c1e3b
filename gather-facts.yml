- name: Setup multiple key access for EE
  hosts: localhost
  become: false
  roles:
    - setup-ssh-config

- name: Gather facts with fallback SSH user
  hosts: all
  gather_facts: false
  tasks:

    - name: Try ubuntu
      block:
        - ansible.builtin.setup:
          remote_user: ubuntu
          register: ubuntu_result
          become: true
      rescue:
        - ansible.builtin.setup:
          remote_user: ubuntu
          register: ubuntu_result
          become: false

    - name: Try cloud-user if ubuntu unreachable
      when: ubuntu_result.unreachable | default(false)
      block:
        - ansible.builtin.setup:
          remote_user: cloud-user
          register: cloud_result
          become: true
      rescue:
        - ansible.builtin.setup:
          remote_user: cloud-user
          register: cloud_result
          become: false

    - name: Try centos if both fail
      when: (ubuntu_result.unreachable | default(false)) and (cloud_result.unreachable | default(false))
      block:
        - ansible.builtin.setup:
          remote_user: centos
          register: centos_result
          become: true
      rescue:
        - ansible.builtin.setup:
          remote_user: centos
          register: centos_result
          become: false
